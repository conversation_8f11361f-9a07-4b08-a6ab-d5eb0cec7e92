package net.aethor.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import net.fabricmc.loader.api.FabricLoader;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.file.Path;

/**
 * <PERSON><PERSON>u hình chung cho QuickSettingKeyMod
 * Quản lý cả default config và runtime config trong file duy nhất
 */
public class ModConfig {

    // Config file management
    private static final String CONFIG_FILE_NAME = "quicksettingkey.json";
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();
    private static final Path CONFIG_DIR = FabricLoader.getInstance().getConfigDir();
    private static final File CONFIG_FILE = CONFIG_DIR.resolve(CONFIG_FILE_NAME).toFile();

    // Static config data loaded from JSON
    private static JsonObject configData;

    // Runtime data
    private static RuntimeData runtimeData = new RuntimeData();

    // Mod Information
    public static final String MOD_ID;
    public static final String MOD_NAME;

    // Feature Enable/Disable Flags
    public static final boolean ENABLE_RENDER_DISTANCE;
    public static final boolean ENABLE_SIMULATION_DISTANCE;
    public static final boolean ENABLE_FOV;
    public static final boolean ENABLE_BRIGHTNESS;
    public static final boolean ENABLE_ZOOM;
    public static final boolean ENABLE_HOSTILE_MOB_SOUND;
    public static final boolean ENABLE_FRIENDLY_MOB_SOUND;
    public static final boolean ENABLE_MUSIC_SOUND;
    public static final boolean ENABLE_JUKEBOX_SOUND;

    // Render Distance Settings
    public static final int MIN_RENDER_DISTANCE;
    public static final int MAX_RENDER_DISTANCE;
    public static final int RENDER_STEP;

    // Simulation Distance Settings
    public static final int MIN_SIMULATION_DISTANCE;
    public static final int MAX_SIMULATION_DISTANCE;
    public static final int SIMULATION_STEP;

    // FOV Settings
    public static final int MIN_FOV;
    public static final int MAX_FOV;
    public static final int FOV_STEP;

    // Zoom Settings
    public static final int ZOOM_FOV; // FOV khi zoom

    // Debouncing Settings
    public static final int DEBOUNCE_DELAY_MS; // milliseconds - delay before applying changes

    // Sound Settings
    public static final double HOSTILE_MOB_VOLUME;
    public static final double FRIENDLY_MOB_VOLUME;
    public static final double MUSIC_VOLUME;
    public static final double JUKEBOX_VOLUME;

    // Brightness Settings
    public static final double BRIGHTNESS_MIN;
    public static final double BRIGHTNESS_MAX;
    public static final double BRIGHTNESS_STEP;

    // Debug Settings
    public static final boolean DEBUG_MODE;

    // Static initializer - load config từ JSON
    static {
        loadConfigFromFile();

        // Initialize constants từ config data
        MOD_ID = getStringValue("mod_info.mod_id", "quicksettingkeymod");
        MOD_NAME = getStringValue("mod_info.mod_name", "QuickSettingKeyMod");

        // Feature Enable/Disable Flags
        ENABLE_RENDER_DISTANCE = getBooleanValue("features.render_distance", true);
        ENABLE_SIMULATION_DISTANCE = getBooleanValue("features.simulation_distance", true);
        ENABLE_FOV = getBooleanValue("features.fov", true);
        ENABLE_BRIGHTNESS = getBooleanValue("features.brightness", true);
        ENABLE_ZOOM = getBooleanValue("features.zoom", true);
        ENABLE_HOSTILE_MOB_SOUND = getBooleanValue("features.hostile_mob_sound", true);
        ENABLE_FRIENDLY_MOB_SOUND = getBooleanValue("features.friendly_mob_sound", true);
        ENABLE_MUSIC_SOUND = getBooleanValue("features.music_sound", true);
        ENABLE_JUKEBOX_SOUND = getBooleanValue("features.jukebox_sound", true);

        // Render Distance Settings
        MIN_RENDER_DISTANCE = getIntValue("render_distance.min", 2);
        MAX_RENDER_DISTANCE = getIntValue("render_distance.max", 32);
        RENDER_STEP = getIntValue("render_distance.step", 1);

        // Simulation Distance Settings
        MIN_SIMULATION_DISTANCE = getIntValue("simulation_distance.min", 5);
        MAX_SIMULATION_DISTANCE = getIntValue("simulation_distance.max", 32);
        SIMULATION_STEP = getIntValue("simulation_distance.step", 1);

        // FOV Settings
        MIN_FOV = getIntValue("fov.min", 30);
        MAX_FOV = getIntValue("fov.max", 110);
        FOV_STEP = getIntValue("fov.step", 5);
        ZOOM_FOV = getIntValue("fov.zoom_fov", 30);

        // Debouncing Settings
        DEBOUNCE_DELAY_MS = getIntValue("debounce.delay_ms", 2000);

        // Sound Settings
        HOSTILE_MOB_VOLUME = getDoubleValue("sound_settings.hostile_mob_volume", 1.0);
        FRIENDLY_MOB_VOLUME = getDoubleValue("sound_settings.friendly_mob_volume", 1.0);
        MUSIC_VOLUME = getDoubleValue("sound_settings.music_volume", 1.0);
        JUKEBOX_VOLUME = getDoubleValue("sound_settings.jukebox_volume", 1.0);

        // Brightness Settings
        BRIGHTNESS_MIN = getDoubleValue("brightness.min", 0.0);
        BRIGHTNESS_MAX = getDoubleValue("brightness.max", 12.0);
        BRIGHTNESS_STEP = getDoubleValue("brightness.step", 0.5);

        // Debug Settings
        DEBUG_MODE = getBooleanValue("debug.enabled", false);
    }

    private ModConfig() {
        // Utility class - không cho phép khởi tạo
    }

    /**
     * Load config từ file (tạo từ template nếu chưa có)
     */
    private static void loadConfigFromFile() {
        // Tạo file từ template nếu chưa tồn tại
        if (!CONFIG_FILE.exists()) {
            copyTemplateToConfigFolder();
        }

        // Load config từ file
        try (FileReader reader = new FileReader(CONFIG_FILE)) {
            configData = JsonParser.parseReader(reader).getAsJsonObject();

            // Load runtime data
            loadRuntimeData();

            if (DEBUG_MODE) {
                System.out.println("[" + MOD_NAME + "] Config loaded successfully");
            }
        } catch (Exception e) {
            System.err.println("[" + MOD_NAME + "] Error loading config: " + e.getMessage());
            // Fallback: load từ template
            loadConfigFromTemplate();
        }
    }

    /**
     * Load config từ template trong resources
     */
    private static void loadConfigFromTemplate() {
        try {
            InputStream inputStream = ModConfig.class.getResourceAsStream("/quicksettingkey.json");
            if (inputStream == null) {
                System.err.println("[" + MOD_NAME + "] Template not found, using empty config");
                configData = new JsonObject();
                return;
            }

            InputStreamReader reader = new InputStreamReader(inputStream);
            configData = JsonParser.parseReader(reader).getAsJsonObject();
            reader.close();
            inputStream.close();

            System.out.println("[" + MOD_NAME + "] Loaded config from template");
        } catch (Exception e) {
            System.err.println("[" + MOD_NAME + "] Error loading template: " + e.getMessage());
            configData = new JsonObject();
        }
    }

    /**
     * Copy template từ resources sang config folder
     */
    private static void copyTemplateToConfigFolder() {
        try {
            // Tạo config directory
            if (!CONFIG_DIR.toFile().exists()) {
                CONFIG_DIR.toFile().mkdirs();
            }

            // Copy template
            try (InputStream inputStream = ModConfig.class.getResourceAsStream("/quicksettingkey.json")) {
                if (inputStream != null) {
                    java.nio.file.Files.copy(inputStream, CONFIG_FILE.toPath());
                    if (DEBUG_MODE) {
                        System.out.println("[" + MOD_NAME + "] Created config from template");
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("[" + MOD_NAME + "] Error copying template: " + e.getMessage());
        }
    }

    /**
     * Load runtime data từ config
     */
    private static void loadRuntimeData() {
        if (configData.has("brightness")) {
            JsonObject brightness = configData.getAsJsonObject("brightness");
            if (brightness.has("value")) {
                runtimeData.currentBrightness = brightness.get("value").getAsDouble();
            }
        }

        if (configData.has("fov")) {
            JsonObject fov = configData.getAsJsonObject("fov");
            if (fov.has("value")) {
                runtimeData.currentFOV = fov.get("value").getAsInt();
            }
        }

        if (configData.has("render_distance")) {
            JsonObject renderDistance = configData.getAsJsonObject("render_distance");
            if (renderDistance.has("value")) {
                runtimeData.currentRenderDistance = renderDistance.get("value").getAsInt();
            }
        }

        if (configData.has("simulation_distance")) {
            JsonObject simulationDistance = configData.getAsJsonObject("simulation_distance");
            if (simulationDistance.has("value")) {
                runtimeData.currentSimulationDistance = simulationDistance.get("value").getAsInt();
            }
        }

        // Load sound volumes
        if (configData.has("sound_settings")) {
            JsonObject soundSettings = configData.getAsJsonObject("sound_settings");
            if (soundSettings.has("hostile_mob_volume")) {
                runtimeData.hostileMobOriginalVolume = soundSettings.get("hostile_mob_volume").getAsDouble();
            }
            if (soundSettings.has("friendly_mob_volume")) {
                runtimeData.friendlyMobOriginalVolume = soundSettings.get("friendly_mob_volume").getAsDouble();
            }
            if (soundSettings.has("music_volume")) {
                runtimeData.musicOriginalVolume = soundSettings.get("music_volume").getAsDouble();
            }
            if (soundSettings.has("jukebox_volume")) {
                runtimeData.jukeboxOriginalVolume = soundSettings.get("jukebox_volume").getAsDouble();
            }
        }
    }

    /**
     * Get string value từ config với nested path (e.g., "mod_info.mod_id")
     */
    private static String getStringValue(String path, String defaultValue) {
        try {
            String[] parts = path.split("\\.");
            JsonObject current = configData;

            for (int i = 0; i < parts.length - 1; i++) {
                if (!current.has(parts[i])) return defaultValue;
                current = current.getAsJsonObject(parts[i]);
            }

            String lastKey = parts[parts.length - 1];
            return current.has(lastKey) ? current.get(lastKey).getAsString() : defaultValue;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * Get int value từ config với nested path
     */
    private static int getIntValue(String path, int defaultValue) {
        try {
            String[] parts = path.split("\\.");
            JsonObject current = configData;

            for (int i = 0; i < parts.length - 1; i++) {
                if (!current.has(parts[i])) return defaultValue;
                current = current.getAsJsonObject(parts[i]);
            }

            String lastKey = parts[parts.length - 1];
            return current.has(lastKey) ? current.get(lastKey).getAsInt() : defaultValue;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * Get double value từ config với nested path
     */
    private static double getDoubleValue(String path, double defaultValue) {
        try {
            String[] parts = path.split("\\.");
            JsonObject current = configData;

            for (int i = 0; i < parts.length - 1; i++) {
                if (!current.has(parts[i])) return defaultValue;
                current = current.getAsJsonObject(parts[i]);
            }

            String lastKey = parts[parts.length - 1];
            return current.has(lastKey) ? current.get(lastKey).getAsDouble() : defaultValue;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * Get boolean value từ config với nested path
     */
    private static boolean getBooleanValue(String path, boolean defaultValue) {
        try {
            String[] parts = path.split("\\.");
            JsonObject current = configData;

            for (int i = 0; i < parts.length - 1; i++) {
                if (!current.has(parts[i])) return defaultValue;
                current = current.getAsJsonObject(parts[i]);
            }

            String lastKey = parts[parts.length - 1];
            return current.has(lastKey) ? current.get(lastKey).getAsBoolean() : defaultValue;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * Generic clamp method
     */
    private static int clamp(int value, int min, int max) {
        return Math.max(min, Math.min(max, value));
    }

    private static double clamp(double value, double min, double max) {
        return Math.max(min, Math.min(max, value));
    }

    /**
     * Clamp render distance vào giới hạn cho phép
     */
    public static int clampRenderDistance(int distance) {
        return clamp(distance, MIN_RENDER_DISTANCE, MAX_RENDER_DISTANCE);
    }

    /**
     * Clamp simulation distance vào giới hạn cho phép
     */
    public static int clampSimulationDistance(int distance) {
        return clamp(distance, MIN_SIMULATION_DISTANCE, MAX_SIMULATION_DISTANCE);
    }

    /**
     * Clamp FOV vào giới hạn cho phép
     */
    public static int clampFOV(int fov) {
        return clamp(fov, MIN_FOV, MAX_FOV);
    }



    // ==================== RUNTIME DATA MANAGEMENT ====================

    /**
     * Save config với current values
     */
    public static void saveConfig() {
        try {
            // Load lại config để đảm bảo structure đầy đủ
            if (CONFIG_FILE.exists()) {
                try (FileReader reader = new FileReader(CONFIG_FILE)) {
                    JsonObject currentConfig = JsonParser.parseReader(reader).getAsJsonObject();
                    updateCurrentValues(currentConfig);

                    try (FileWriter writer = new FileWriter(CONFIG_FILE)) {
                        GSON.toJson(currentConfig, writer);
                    }
                }
            }

            if (DEBUG_MODE) {
                System.out.println("[" + MOD_NAME + "] Config saved successfully");
            }
        } catch (IOException e) {
            System.err.println("[" + MOD_NAME + "] Error saving config: " + e.getMessage());
        }
    }

    /**
     * Update values trong JSON
     */
    private static void updateCurrentValues(JsonObject config) {
        if (config.has("brightness")) {
            config.getAsJsonObject("brightness").addProperty("value", runtimeData.currentBrightness);
        }
        if (config.has("fov")) {
            config.getAsJsonObject("fov").addProperty("value", runtimeData.currentFOV);
        }
        if (config.has("render_distance")) {
            config.getAsJsonObject("render_distance").addProperty("value", runtimeData.currentRenderDistance);
        }
        if (config.has("simulation_distance")) {
            config.getAsJsonObject("simulation_distance").addProperty("value", runtimeData.currentSimulationDistance);
        }

        // Update sound volumes
        if (config.has("sound_settings")) {
            JsonObject soundSettings = config.getAsJsonObject("sound_settings");
            soundSettings.addProperty("hostile_mob_volume", runtimeData.hostileMobOriginalVolume);
            soundSettings.addProperty("friendly_mob_volume", runtimeData.friendlyMobOriginalVolume);
            soundSettings.addProperty("music_volume", runtimeData.musicOriginalVolume);
            soundSettings.addProperty("jukebox_volume", runtimeData.jukeboxOriginalVolume);
        }
    }

    // ==================== BRIGHTNESS MANAGEMENT ====================

    public static double getCurrentBrightness() {
        return runtimeData.currentBrightness;
    }

    public static void setCurrentBrightness(double brightness) {
        runtimeData.currentBrightness = clamp(brightness, BRIGHTNESS_MIN, BRIGHTNESS_MAX);
        saveConfig();
    }

    // ==================== FOV MANAGEMENT ====================

    public static int getCurrentFOV() {
        return runtimeData.currentFOV;
    }

    public static void setCurrentFOV(int fov) {
        runtimeData.currentFOV = clampFOV(fov);
        saveConfig();
    }

    // ==================== RENDER DISTANCE MANAGEMENT ====================

    public static int getCurrentRenderDistance() {
        return runtimeData.currentRenderDistance;
    }

    public static void setCurrentRenderDistance(int distance) {
        runtimeData.currentRenderDistance = clampRenderDistance(distance);
        saveConfig();
    }

    // ==================== SIMULATION DISTANCE MANAGEMENT ====================

    public static int getCurrentSimulationDistance() {
        return runtimeData.currentSimulationDistance;
    }

    public static void setCurrentSimulationDistance(int distance) {
        runtimeData.currentSimulationDistance = clampSimulationDistance(distance);
        saveConfig();
    }

    // ==================== SOUND VOLUME MANAGEMENT ====================

    public static double getHostileMobOriginalVolume() {
        return runtimeData.hostileMobOriginalVolume;
    }

    public static void setHostileMobOriginalVolume(double volume) {
        runtimeData.hostileMobOriginalVolume = volume;
        saveConfig();
    }

    public static double getFriendlyMobOriginalVolume() {
        return runtimeData.friendlyMobOriginalVolume;
    }

    public static void setFriendlyMobOriginalVolume(double volume) {
        runtimeData.friendlyMobOriginalVolume = volume;
        saveConfig();
    }

    public static double getMusicOriginalVolume() {
        return runtimeData.musicOriginalVolume;
    }

    public static void setMusicOriginalVolume(double volume) {
        runtimeData.musicOriginalVolume = volume;
        saveConfig();
    }

    public static double getJukeboxOriginalVolume() {
        return runtimeData.jukeboxOriginalVolume;
    }

    public static void setJukeboxOriginalVolume(double volume) {
        runtimeData.jukeboxOriginalVolume = volume;
        saveConfig();
    }

    /**
     * Runtime data storage
     */
    private static class RuntimeData {
        // Graphics settings - sẽ được load từ config
        double currentBrightness = 1.0;
        int currentFOV = 70;
        int currentRenderDistance = 12;
        int currentSimulationDistance = 12;

        // Sound volumes - lưu original volume để restore (-1 = chưa khởi tạo)
        double hostileMobOriginalVolume = -1.0;
        double friendlyMobOriginalVolume = -1.0;
        double musicOriginalVolume = -1.0;
        double jukeboxOriginalVolume = -1.0;
    }
}