package net.aethor.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import net.fabricmc.loader.api.FabricLoader;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Path;

/**
 * Quản lý cấu hình của mod
 * Sử dụng file quicksettingkey.json duy nhất để lưu trữ cả default và current values
 */
public class ConfigManager {

    private static final String CONFIG_FILE_NAME = "quicksettingkey.json";
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();
    private static final Path CONFIG_DIR = FabricLoader.getInstance().getConfigDir();
    private static final File CONFIG_FILE = CONFIG_DIR.resolve(CONFIG_FILE_NAME).toFile();

    // Config instance
    private static ConfigData configData = new ConfigData();

    /**
     * Load cấu hình từ file JSON
     */
    public static void loadConfig() {
        if (!CONFIG_FILE.exists()) {
            // Copy file từ resources sang config folder
            copyDefaultConfigFromResources();
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Created config file from resources");
            }
        }

        try (FileReader reader = new FileReader(CONFIG_FILE)) {
            JsonObject jsonObject = JsonParser.parseReader(reader).getAsJsonObject();

            // Load brightness settings
            if (jsonObject.has("brightness")) {
                JsonObject brightness = jsonObject.getAsJsonObject("brightness");
                if (brightness.has("current_value")) {
                    configData.currentBrightness = brightness.get("current_value").getAsDouble();
                }
            }

            // Load FOV settings
            if (jsonObject.has("fov")) {
                JsonObject fov = jsonObject.getAsJsonObject("fov");
                if (fov.has("current_value")) {
                    configData.currentFOV = fov.get("current_value").getAsInt();
                }
            }

            // Load render distance settings
            if (jsonObject.has("render_distance")) {
                JsonObject renderDistance = jsonObject.getAsJsonObject("render_distance");
                if (renderDistance.has("current_value")) {
                    configData.currentRenderDistance = renderDistance.get("current_value").getAsInt();
                }
            }

            // Load simulation distance settings
            if (jsonObject.has("simulation_distance")) {
                JsonObject simulationDistance = jsonObject.getAsJsonObject("simulation_distance");
                if (simulationDistance.has("current_value")) {
                    configData.currentSimulationDistance = simulationDistance.get("current_value").getAsInt();
                }
            }

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Config loaded successfully");
                System.out.println("[" + ModConfig.MOD_NAME + "] Current Brightness: " + configData.currentBrightness);
            }

        } catch (Exception e) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Error loading config: " + e.getMessage());
            // Fallback to default values
            configData = new ConfigData();
        }
    }

    /**
     * Copy default config từ resources sang config folder
     */
    private static void copyDefaultConfigFromResources() {
        try {
            // Tạo config directory nếu chưa tồn tại
            if (!CONFIG_DIR.toFile().exists()) {
                CONFIG_DIR.toFile().mkdirs();
            }

            // Copy file từ resources
            try (var inputStream = ConfigManager.class.getResourceAsStream("/quicksettingkey.json")) {
                if (inputStream != null) {
                    java.nio.file.Files.copy(inputStream, CONFIG_FILE.toPath());
                    if (ModConfig.DEBUG_MODE) {
                        System.out.println("[" + ModConfig.MOD_NAME + "] Copied default config from resources");
                    }
                } else {
                    // Nếu không tìm thấy file trong resources, tạo config mặc định
                    createDefaultConfig();
                }
            }
        } catch (Exception e) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Error copying config from resources: " + e.getMessage());
            // Fallback: tạo config mặc định
            createDefaultConfig();
        }
    }

    /**
     * Tạo config mặc định nếu không thể copy từ resources
     */
    private static void createDefaultConfig() {
        configData = new ConfigData();
        saveConfig();
    }

    /**
     * Save cấu hình vào file JSON (cập nhật current values)
     */
    public static void saveConfig() {
        try {
            // Đọc config hiện tại để giữ lại structure
            JsonObject root;
            if (CONFIG_FILE.exists()) {
                try (FileReader reader = new FileReader(CONFIG_FILE)) {
                    root = JsonParser.parseReader(reader).getAsJsonObject();
                } catch (Exception e) {
                    // Nếu không đọc được, tạo mới
                    root = createDefaultJsonStructure();
                }
            } else {
                root = createDefaultJsonStructure();
            }

            // Cập nhật current values
            updateCurrentValues(root);

            // Save file
            try (FileWriter writer = new FileWriter(CONFIG_FILE)) {
                GSON.toJson(root, writer);
            }

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Config saved successfully");
            }

        } catch (IOException e) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Error saving config: " + e.getMessage());
        }
    }

    /**
     * Tạo cấu trúc JSON mặc định
     */
    private static JsonObject createDefaultJsonStructure() {
        JsonObject root = new JsonObject();

        // Mod info
        JsonObject modInfo = new JsonObject();
        modInfo.addProperty("mod_id", ModConfig.MOD_ID);
        modInfo.addProperty("mod_name", ModConfig.MOD_NAME);
        modInfo.addProperty("config_version", "1.0");
        root.add("mod_info", modInfo);

        // Brightness
        JsonObject brightness = new JsonObject();
        brightness.addProperty("min", ModConfig.BRIGHTNESS_MIN);
        brightness.addProperty("max", ModConfig.BRIGHTNESS_MAX);
        brightness.addProperty("step", ModConfig.BRIGHTNESS_STEP);
        brightness.addProperty("default_value", ModConfig.getDefaultBrightness());
        brightness.addProperty("current_value", configData.currentBrightness);
        root.add("brightness", brightness);

        // FOV
        JsonObject fov = new JsonObject();
        fov.addProperty("min", ModConfig.MIN_FOV);
        fov.addProperty("max", ModConfig.MAX_FOV);
        fov.addProperty("default_step", ModConfig.DEFAULT_FOV_STEP);
        fov.addProperty("default_value", ModConfig.DEFAULT_FOV);
        fov.addProperty("zoom_fov", ModConfig.ZOOM_FOV);
        fov.addProperty("current_value", configData.currentFOV);
        root.add("fov", fov);

        // Render distance
        JsonObject renderDistance = new JsonObject();
        renderDistance.addProperty("min", ModConfig.MIN_RENDER_DISTANCE);
        renderDistance.addProperty("max", ModConfig.MAX_RENDER_DISTANCE);
        renderDistance.addProperty("default_step", ModConfig.DEFAULT_RENDER_STEP);
        renderDistance.addProperty("default_value", ModConfig.getDefaultRenderDistance());
        renderDistance.addProperty("current_value", configData.currentRenderDistance);
        root.add("render_distance", renderDistance);

        // Simulation distance
        JsonObject simulationDistance = new JsonObject();
        simulationDistance.addProperty("min", ModConfig.MIN_SIMULATION_DISTANCE);
        simulationDistance.addProperty("max", ModConfig.MAX_SIMULATION_DISTANCE);
        simulationDistance.addProperty("default_step", ModConfig.DEFAULT_SIMULATION_STEP);
        simulationDistance.addProperty("default_value", ModConfig.getDefaultSimulationDistance());
        simulationDistance.addProperty("current_value", configData.currentSimulationDistance);
        root.add("simulation_distance", simulationDistance);

        return root;
    }

    /**
     * Cập nhật current values trong JSON object
     */
    private static void updateCurrentValues(JsonObject root) {
        // Update brightness current value
        if (root.has("brightness")) {
            JsonObject brightness = root.getAsJsonObject("brightness");
            brightness.addProperty("current_value", configData.currentBrightness);
            brightness.addProperty("enabled", configData.brightnessEnabled);
        }

        // Update FOV current value
        if (root.has("fov")) {
            JsonObject fov = root.getAsJsonObject("fov");
            fov.addProperty("current_value", configData.currentFOV);
        }

        // Update render distance current value
        if (root.has("render_distance")) {
            JsonObject renderDistance = root.getAsJsonObject("render_distance");
            renderDistance.addProperty("current_value", configData.currentRenderDistance);
        }

        // Update simulation distance current value
        if (root.has("simulation_distance")) {
            JsonObject simulationDistance = root.getAsJsonObject("simulation_distance");
            simulationDistance.addProperty("current_value", configData.currentSimulationDistance);
        }
    }

    // Getters và setters cho brightness
    public static double getCurrentBrightness() {
        return configData.currentBrightness;
    }

    public static void setCurrentBrightness(double brightness) {
        configData.currentBrightness = Math.max(ModConfig.BRIGHTNESS_MIN,
                Math.min(ModConfig.BRIGHTNESS_MAX, brightness));
        saveConfig(); // Auto-save khi thay đổi
    }

    public static boolean isBrightnessEnabled() {
        return configData.brightnessEnabled;
    }

    public static void setBrightnessEnabled(boolean enabled) {
        configData.brightnessEnabled = enabled;
        saveConfig();
    }

    // Getters và setters cho FOV
    public static int getCurrentFOV() {
        return configData.currentFOV;
    }

    public static void setCurrentFOV(int fov) {
        configData.currentFOV = ModConfig.clampFOV(fov);
        saveConfig();
    }

    // Getters và setters cho render distance
    public static int getCurrentRenderDistance() {
        return configData.currentRenderDistance;
    }

    public static void setCurrentRenderDistance(int distance) {
        configData.currentRenderDistance = ModConfig.clampRenderDistance(distance);
        saveConfig();
    }

    // Getters và setters cho simulation distance
    public static int getCurrentSimulationDistance() {
        return configData.currentSimulationDistance;
    }

    public static void setCurrentSimulationDistance(int distance) {
        configData.currentSimulationDistance = ModConfig.clampSimulationDistance(distance);
        saveConfig();
    }

    /**
     * Class chứa dữ liệu cấu hình runtime
     */
    private static class ConfigData {
        // Graphics settings với giá trị mặc định từ ModConfig
        public double currentBrightness = ModConfig.getDefaultBrightness();
        public boolean brightnessEnabled = true;
        public int currentFOV = ModConfig.DEFAULT_FOV;
        public int currentRenderDistance = ModConfig.getDefaultRenderDistance();
        public int currentSimulationDistance = ModConfig.getDefaultSimulationDistance();
    }
}
