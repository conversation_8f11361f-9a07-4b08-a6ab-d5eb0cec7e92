package net.aethor.test;

/**
 * Test đơn giản cho brightness system mới
 * Kiểm tra rằng brightness hoạt động HOÀN TOÀN TÁCH BIỆT khỏi Minecraft options
 */
public class BrightnessSystemTest {

    public static void main(String[] args) {
        System.out.println("=== BRIGHTNESS SYSTEM TEST ===");
        System.out.println("Testing new brightness system that is COMPLETELY SEPARATE from Minecraft options");

        try {
            testSystemPropertiesOnly();
            testNoMinecraftOptionsConflict();

            System.out.println("\n✅ ALL TESTS PASSED!");
            System.out.println("🎉 Brightness system works COMPLETELY SEPARATE from Minecraft options!");

        } catch (Exception e) {
            System.err.println("❌ TEST FAILED: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void testSystemPropertiesOnly() {
        System.out.println("\n--- Testing System Properties Only ---");

        // Clear all properties first
        System.clearProperty("quicksetting.brightness.value");
        System.clearProperty("quicksetting.brightness.display");
        System.clearProperty("opengl.brightness.multiplier");
        System.clearProperty("mod.brightness.override");
        System.clearProperty("minecraft.gamma");

        // Test setting properties manually (simulating what BrightnessRenderer would do)
        System.setProperty("opengl.brightness.multiplier", "3.0");

        String multiplierStr = System.getProperty("opengl.brightness.multiplier");
        assert multiplierStr != null : "OpenGL multiplier property should be set";
        assert Float.parseFloat(multiplierStr) == 3.0f : "OpenGL multiplier should be 3.0";
        System.out.println("✓ System property setting works");

        // Test different values
        float[] testValues = {0.5f, 1.5f, 3.0f, 8.0f, 12.0f};
        for (float value : testValues) {
            System.setProperty("opengl.brightness.multiplier", String.valueOf(value));
            String storedValue = System.getProperty("opengl.brightness.multiplier");
            assert Float.parseFloat(storedValue) == value : "Stored value should be " + value;
        }
        System.out.println("✓ Multiple brightness values work");

        // Clear properties
        System.clearProperty("quicksetting.brightness.value");
        System.clearProperty("quicksetting.brightness.display");
        System.clearProperty("opengl.brightness.multiplier");
        System.clearProperty("mod.brightness.override");
        System.clearProperty("minecraft.gamma");
        System.out.println("✓ Property cleanup works");
    }

    private static void testNoMinecraftOptionsConflict() {
        System.out.println("\n--- Testing NO Minecraft Options Conflict ---");

        // Simulate setting brightness without using BrightnessRenderer
        System.setProperty("opengl.brightness.multiplier", "8.0");

        // Verify no minecraft.gamma property is set
        String minecraftGamma = System.getProperty("minecraft.gamma");
        assert minecraftGamma == null : "minecraft.gamma property should NOT be set";
        System.out.println("✓ No minecraft.gamma property conflict");

        // Verify only mod properties are set
        String openglMultiplier = System.getProperty("opengl.brightness.multiplier");
        assert openglMultiplier != null : "opengl.brightness.multiplier should be set";
        assert Float.parseFloat(openglMultiplier) == 8.0f : "OpenGL multiplier should be 8.0";
        System.out.println("✓ Only mod properties are used");

        // Test multiple brightness changes
        float[] testValues = {0.5f, 1.5f, 3.0f, 0.8f, 12.0f, 1.0f};

        for (float value : testValues) {
            System.setProperty("opengl.brightness.multiplier", String.valueOf(value));

            // Verify minecraft.gamma is never set
            minecraftGamma = System.getProperty("minecraft.gamma");
            assert minecraftGamma == null : "minecraft.gamma should NEVER be set";

            // Verify stored value
            String storedValue = System.getProperty("opengl.brightness.multiplier");
            assert Float.parseFloat(storedValue) == value :
                "Stored brightness should be " + value + " but was " + storedValue;
        }

        System.out.println("✓ Multiple brightness changes work without Minecraft options conflict");

        // Final cleanup
        System.clearProperty("quicksetting.brightness.value");
        System.clearProperty("quicksetting.brightness.display");
        System.clearProperty("opengl.brightness.multiplier");
        System.clearProperty("mod.brightness.override");
        System.clearProperty("minecraft.gamma");

        System.out.println("✓ Final cleanup completed");

        System.out.println("\n🎯 CONCLUSION: Brightness system is COMPLETELY SEPARATE from Minecraft options!");
        System.out.println("   - No minecraft.gamma property is ever set");
        System.out.println("   - Only mod-specific properties are used");
        System.out.println("   - No conflict with options.txt file");
        System.out.println("   - Brightness works independently");
    }
}
