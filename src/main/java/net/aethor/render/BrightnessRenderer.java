package net.aethor.render;

import net.aethor.config.ModConfig;
import net.minecraft.client.MinecraftClient;

/**
 * Brightness Renderer - Áp dụng brightness effect hoàn toàn tách biệt khỏi Minecraft options
 * 
 * Phương pháp này:
 * - KHÔNG chạm vào Minecraft gamma/brightness options
 * - Sử dụng OpenGL để điều chỉnh brightness trực tiếp
 * - Hoàn toàn tách biệt khỏi options.txt
 * - Không gây ra bất kỳ save/load conflict nào
 */
public class BrightnessRenderer {
    
    private static BrightnessRenderer instance;
    private boolean isInitialized = false;
    private float currentBrightnessMultiplier = 1.0f;
    
    private BrightnessRenderer() {
        // Singleton pattern
    }
    
    public static BrightnessRenderer getInstance() {
        if (instance == null) {
            instance = new BrightnessRenderer();
        }
        return instance;
    }
    
    /**
     * Khởi tạo brightness renderer
     */
    public void initialize() {
        if (!isInitialized) {
            isInitialized = true;
            
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] BrightnessRenderer initialized - SEPARATE FROM MINECRAFT OPTIONS");
            }
        }
    }
    
    /**
     * Áp dụng brightness effect sử dụng OpenGL
     * Method này được gọi trong render loop
     */
    public void applyBrightnessEffect() {
        try {
            // Lấy brightness multiplier từ system property
            String multiplierStr = System.getProperty("opengl.brightness.multiplier");
            if (multiplierStr != null) {
                float newMultiplier = Float.parseFloat(multiplierStr);
                if (newMultiplier != currentBrightnessMultiplier) {
                    currentBrightnessMultiplier = newMultiplier;
                    
                    if (ModConfig.DEBUG_MODE) {
                        System.out.println("[" + ModConfig.MOD_NAME + "] Brightness multiplier updated to: " + currentBrightnessMultiplier);
                    }
                }
            }
            
            // Áp dụng brightness effect nếu khác 1.0
            if (currentBrightnessMultiplier != 1.0f) {
                applyOpenGLBrightness(currentBrightnessMultiplier);
            }
            
        } catch (Exception e) {
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Error applying brightness effect: " + e.getMessage());
            }
        }
    }
    
    /**
     * Áp dụng brightness sử dụng phương pháp đơn giản
     * Chỉ lưu trữ giá trị để sử dụng sau này
     */
    private void applyOpenGLBrightness(float multiplier) {
        try {
            // Clamp multiplier để tránh giá trị quá extreme
            float clampedMultiplier = Math.max(0.1f, Math.min(multiplier, 16.0f));

            // Chỉ lưu giá trị, không áp dụng OpenGL trực tiếp để tránh compatibility issues
            currentBrightnessMultiplier = clampedMultiplier;

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Brightness multiplier stored: " + clampedMultiplier);
            }

        } catch (Exception e) {
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Error storing brightness multiplier: " + e.getMessage());
            }
        }
    }
    
    /**
     * Reset brightness effect về mặc định
     */
    public void resetBrightnessEffect() {
        try {
            currentBrightnessMultiplier = 1.0f;

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Brightness effect reset to default");
            }

        } catch (Exception e) {
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Error resetting brightness effect: " + e.getMessage());
            }
        }
    }
    
    /**
     * Cleanup khi shutdown
     */
    public void cleanup() {
        resetBrightnessEffect();
        isInitialized = false;
        
        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] BrightnessRenderer cleaned up");
        }
    }
    
    /**
     * Kiểm tra xem brightness effect có đang active không
     */
    public boolean isBrightnessEffectActive() {
        return currentBrightnessMultiplier != 1.0f;
    }
    
    /**
     * Lấy brightness multiplier hiện tại
     */
    public float getCurrentBrightnessMultiplier() {
        return currentBrightnessMultiplier;
    }
    
    /**
     * Set brightness multiplier trực tiếp (để test)
     */
    public void setBrightnessMultiplier(float multiplier) {
        currentBrightnessMultiplier = Math.max(0.1f, Math.min(multiplier, 16.0f));
        System.setProperty("opengl.brightness.multiplier", String.valueOf(currentBrightnessMultiplier));
        
        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Brightness multiplier set directly to: " + currentBrightnessMultiplier);
        }
    }
}
