package net.aethor.features;

import net.aethor.config.ModConfig;
import net.aethor.utils.TranslationKeys;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.util.InputUtil;

/**
 * Feature để điều khiển Render Distance bằng phím tắt
 * Kế thừa từ AbstractDistanceFeature với debouncing logic
 */
public class RenderDistanceFeature extends AbstractDistanceFeature {

    private boolean renderDistanceLoaded = false;

    public RenderDistanceFeature() {
        super(
                TranslationKeys.RENDER_DISTANCE_FEATURE,
                TranslationKeys.INCREASE_RENDER_DISTANCE,
                TranslationKeys.DECREASE_RENDER_DISTANCE,
                TranslationKeys.QUICK_SETTING_CATEGORY,
                InputUtil.UNKNOWN_KEY.getCode(), // Không có phím mặc định
                InputUtil.UNKNOWN_KEY.getCode(), // Không có phím mặc định
                ModConfig.MIN_RENDER_DISTANCE,
                ModConfig.MAX_RENDER_DISTANCE,
                ModConfig.RENDER_STEP
        );
    }

    @Override
    protected int getCurrentValue(MinecraftClient client) {
        // Load từ config lần đầu khi player join
        if (!renderDistanceLoaded && client.player != null) {
            loadAndApplyRenderDistance(client);
            renderDistanceLoaded = true;
        }
        return client.options.getViewDistance().getValue();
    }

    @Override
    protected void setValue(MinecraftClient client, int value) {
        int clampedValue = ModConfig.clampRenderDistance(value);
        client.options.getViewDistance().setValue(clampedValue);
        // Lưu vào config
        ModConfig.setCurrentRenderDistance(clampedValue);

        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Render Distance changed to: " + clampedValue);
        }
    }

    /**
     * Load render distance từ config và apply vào game
     */
    private void loadAndApplyRenderDistance(MinecraftClient client) {
        int savedRenderDistance = ModConfig.getCurrentRenderDistance();
        client.options.getViewDistance().setValue(savedRenderDistance);

        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Loaded render distance from config: " + savedRenderDistance);
        }
    }

    @Override
    protected String getFeatureNameKey() {
        return TranslationKeys.RENDER_DISTANCE_NAME;
    }

    @Override
    protected void showChangeMessage(MinecraftClient client, int newValue) {
        sendFeatureChangedMessage(client, TranslationKeys.RENDER_DISTANCE_CHANGED, newValue);
    }

    @Override
    protected void showMaxMessage(MinecraftClient client) {
        sendFeatureMessage(client, TranslationKeys.RENDER_DISTANCE_MAX, TranslationKeys.MESSAGE_MAX, maxValue);
    }

    @Override
    protected void showMinMessage(MinecraftClient client) {
        sendFeatureMessage(client, TranslationKeys.RENDER_DISTANCE_MIN, TranslationKeys.MESSAGE_MIN, minValue);
    }

    @Override
    public boolean isEnabled() {
        return ModConfig.ENABLE_RENDER_DISTANCE;
    }
}