package net.aethor.features;

import net.aethor.config.ModConfig;
import net.aethor.utils.TranslationKeys;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import net.minecraft.text.Text;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * Feature để tăng/giảm độ sáng (brightness/gamma) bằng phím tắt
 * Cho phép điều chỉnh từ 0% (tối nhất) đến 1600% (sáng nhất)
 * Sử dụng reflection để bypass giới hạn gamma của Minecraft
 */
public class BrightnessFeature {

    private final KeyBinding increaseBrightnessKey;
    private final KeyBinding decreaseBrightnessKey;
    private Field gammaField;
    private boolean reflectionWorking = false;
    private boolean brightnessLoaded = false;

    public BrightnessFeature() {
        // Đăng ký key bindings
        this.increaseBrightnessKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
                TranslationKeys.INCREASE_BRIGHTNESS,
                InputUtil.Type.KEYSYM,
                InputUtil.UNKNOWN_KEY.getCode(),
                TranslationKeys.QUICK_SETTING_CATEGORY
        ));

        this.decreaseBrightnessKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
                TranslationKeys.DECREASE_BRIGHTNESS,
                InputUtil.Type.KEYSYM,
                InputUtil.UNKNOWN_KEY.getCode(),
                TranslationKeys.QUICK_SETTING_CATEGORY
        ));

        // Khởi tạo reflection để truy cập trực tiếp vào gamma field
        initReflection();
    }

    /**
     * Khởi tạo reflection để truy cập gamma field
     */
    private void initReflection() {
        try {
            // Tìm gamma field trong GameOptions
            // Tên field có thể khác nhau tùy version và mapping
            String[] possibleFieldNames = {"gamma", "field_1846", "f_92117_", "ao"}; // Các tên field có thể có

            Class<?> gameOptionsClass = MinecraftClient.getInstance().options.getClass();

            for (String fieldName : possibleFieldNames) {
                try {
                    gammaField = gameOptionsClass.getDeclaredField(fieldName);
                    gammaField.setAccessible(true);

                    // Test xem field có hoạt động không
                    Object fieldValue = gammaField.get(MinecraftClient.getInstance().options);
                    if (fieldValue != null) {
                        reflectionWorking = true;
                        if (ModConfig.DEBUG_MODE) {
                            System.out.println("[" + ModConfig.MOD_NAME + "] Found gamma field: " + fieldName);
                        }
                        break;
                    }
                } catch (NoSuchFieldException ignored) {
                    // Tiếp tục thử field name khác
                }
            }

            if (!reflectionWorking && ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Warning: Could not find gamma field, using alternative method");
            }

        } catch (Exception e) {
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Error initializing reflection: " + e.getMessage());
            }
            reflectionWorking = false;
        }
    }

    /**
     * Xử lý input từ bàn phím cho brightness feature
     */
    public void handleInput(MinecraftClient client) {
        if (client.player == null) return;

        // Load brightness từ config lần đầu khi player join
        if (!brightnessLoaded) {
            loadAndApplyBrightness();
            brightnessLoaded = true;
        }

        while (increaseBrightnessKey.wasPressed()) {
            increaseBrightness(client);
        }

        while (decreaseBrightnessKey.wasPressed()) {
            decreaseBrightness(client);
        }
    }

    /**
     * Tăng brightness
     */
    private void increaseBrightness(MinecraftClient client) {
        double currentBrightness = getCurrentBrightnessValue(client);
        double newBrightness = Math.min(currentBrightness + ModConfig.BRIGHTNESS_STEP, ModConfig.BRIGHTNESS_MAX);

        if (newBrightness != currentBrightness) {
            setBrightnessValue(client, newBrightness);
            // Lưu vào ModConfig
            ModConfig.setCurrentBrightness(newBrightness);
            showBrightnessMessage(client, newBrightness, TranslationKeys.BRIGHTNESS_CHANGED);

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Brightness increased to: " +
                        String.format("%.1f", newBrightness) + " (" + String.format("%.0f", newBrightness * 100) + "%)");
            }
        } else {
            showBrightnessMessage(client, newBrightness, TranslationKeys.BRIGHTNESS_MAX);
        }
    }

    /**
     * Giảm brightness
     */
    private void decreaseBrightness(MinecraftClient client) {
        double currentBrightness = getCurrentBrightnessValue(client);
        double newBrightness = Math.max(currentBrightness - ModConfig.BRIGHTNESS_STEP, ModConfig.BRIGHTNESS_MIN);

        if (newBrightness != currentBrightness) {
            setBrightnessValue(client, newBrightness);
            // Lưu vào ModConfig
            ModConfig.setCurrentBrightness(newBrightness);
            showBrightnessMessage(client, newBrightness, TranslationKeys.BRIGHTNESS_CHANGED);

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Brightness decreased to: " +
                        String.format("%.1f", newBrightness) + " (" + String.format("%.0f", newBrightness * 100) + "%)");
            }
        } else {
            showBrightnessMessage(client, newBrightness, TranslationKeys.BRIGHTNESS_MIN);
        }
    }

    /**
     * Load brightness từ config và apply vào game
     */
    private void loadAndApplyBrightness() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client != null) {
            double savedBrightness = ModConfig.getCurrentBrightness();
            setBrightnessValue(client, savedBrightness);

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Loaded brightness from config: " +
                        String.format("%.1f", savedBrightness) + " (" + String.format("%.0f", savedBrightness * 100) + "%)");
            }
        }
    }

    /**
     * Lấy giá trị brightness hiện tại (sử dụng reflection nếu có thể)
     */
    private double getCurrentBrightnessValue(MinecraftClient client) {
        if (reflectionWorking && gammaField != null) {
            try {
                Object gammaOption = gammaField.get(client.options);
                if (gammaOption != null) {
                    // Gamma option có thể là DoubleOption hoặc SimpleOption
                    Method getValueMethod = gammaOption.getClass().getMethod("getValue");
                    Object value = getValueMethod.invoke(gammaOption);
                    return ((Number) value).doubleValue();
                }
            } catch (Exception e) {
                if (ModConfig.DEBUG_MODE) {
                    System.out.println("[" + ModConfig.MOD_NAME + "] Error getting brightness value: " + e.getMessage());
                }
            }
        }

        // Fallback method
        return client.options.getGamma().getValue();
    }

    /**
     * Đặt giá trị brightness (sử dụng reflection để bypass giới hạn)
     * KHÔNG save vào file để tránh error messages
     */
    private void setBrightnessValue(MinecraftClient client, double brightness) {
        // Method 1: Sử dụng reflection để truy cập trực tiếp (KHÔNG save)
        if (reflectionWorking && gammaField != null) {
            try {
                Object gammaOption = gammaField.get(client.options);
                if (gammaOption != null) {
                    // Tìm setValue method
                    Method setValueMethod = gammaOption.getClass().getMethod("setValue", Object.class);
                    setValueMethod.invoke(gammaOption, brightness);

                    // KHÔNG gọi client.options.write() để tránh error khi save
                    if (ModConfig.DEBUG_MODE) {
                        System.out.println("[" + ModConfig.MOD_NAME + "] Successfully set brightness via reflection: " + brightness);
                    }
                    return;
                }
            } catch (Exception e) {
                if (ModConfig.DEBUG_MODE) {
                    System.out.println("[" + ModConfig.MOD_NAME + "] Error setting brightness with reflection: " + e.getMessage());
                }
            }
        }

        // Method 2: Direct field access (KHÔNG save)
        try {
            Field gammaValueField = client.options.getGamma().getClass().getDeclaredField("value");
            gammaValueField.setAccessible(true);
            gammaValueField.set(client.options.getGamma(), brightness);

            // KHÔNG gọi client.options.write() để tránh error
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Successfully set brightness via direct field access: " + brightness);
            }
            return;
        } catch (Exception e) {
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Error with direct field access: " + e.getMessage());
            }
        }

        // Method 3: Alternative approach using system properties (KHÔNG save)
        try {
            System.setProperty("minecraft.gamma", String.valueOf(brightness));
            // Sau đó force update render
            if (client.worldRenderer != null) {
                client.worldRenderer.reload();
            }
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Successfully set brightness via system property: " + brightness);
            }
            return;
        } catch (Exception e) {
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Error with system property method: " + e.getMessage());
            }
        }

        // Fallback: Sử dụng method thông thường (sẽ bị giới hạn)
        try {
            client.options.getGamma().setValue(brightness);
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Fallback method used (limited to 1.0): " + brightness);
            }
        } catch (Exception e) {
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] All methods failed: " + e.getMessage());
            }
        }
    }

    /**
     * Hiển thị thông báo brightness hiện tại
     */
    private void showBrightnessMessage(MinecraftClient client, double brightness, String messageType) {
        if (client.player != null) {
            Text featureName = Text.translatable(TranslationKeys.BRIGHTNESS_NAME);

            int brightnessPercent = (int) Math.round(brightness * 100);
            String color = getColorForBrightness(brightness);
            String message = featureName.getString() + "§7: " + color + brightnessPercent + "%";

            client.player.sendMessage(Text.literal(message), true);
        }
    }

    /**
     * Lấy màu sắc tương ứng với mức brightness
     */
    private String getColorForBrightness(double brightness) {
        if (brightness <= 0.0) {
            return "§8"; // Dark Gray
        } else if (brightness <= 0.5) {
            return "§7"; // Gray
        } else if (brightness <= 1.0) {
            return "§f"; // White
        } else if (brightness <= 5.0) {
            return "§e"; // Yellow
        } else if (brightness <= 10.0) {
            return "§6"; // Gold
        } else {
            return "§c"; // Red
        }
    }

    // Getters và utility methods
    public String getFeatureName() {
        return TranslationKeys.BRIGHTNESS_FEATURE;
    }

    public KeyBinding getIncreaseBrightnessKey() {
        return increaseBrightnessKey;
    }

    public KeyBinding getDecreaseBrightnessKey() {
        return decreaseBrightnessKey;
    }

    public boolean isEnabled() {
        return ModConfig.ENABLE_BRIGHTNESS;
    }

    public double getCurrentBrightness(MinecraftClient client) {
        return getCurrentBrightnessValue(client);
    }

    public int getCurrentBrightnessPercent(MinecraftClient client) {
        return (int) Math.round(getCurrentBrightness(client) * 100);
    }
}