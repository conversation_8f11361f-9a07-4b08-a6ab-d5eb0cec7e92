package net.aethor.features;

import net.aethor.config.ModConfig;
import net.aethor.utils.TranslationKeys;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.util.InputUtil;

/**
 * Feature để điều khiển FOV (Field of View) bằng phím tắt
 * Kế thừa từ AbstractDistanceFeature với debouncing logic
 */
public class FOVFeature extends AbstractDistanceFeature {

    private boolean fovLoaded = false;

    public FOVFeature() {
        super(
                TranslationKeys.FOV_FEATURE,
                TranslationKeys.INCREASE_FOV,
                TranslationKeys.DECREASE_FOV,
                TranslationKeys.QUICK_SETTING_CATEGORY,
                InputUtil.UNKNOWN_KEY.getCode(), // Không có phím mặc định
                InputUtil.UNKNOWN_KEY.getCode(), // Không có phím mặc định
                ModConfig.MIN_FOV,
                ModConfig.MAX_FOV,
                ModConfig.FOV_STEP
        );
    }

    @Override
    protected int getCurrentValue(MinecraftClient client) {
        // Load từ config lần đầu khi player join
        if (!fovLoaded && client.player != null) {
            loadAndApplyFOV(client);
            fovLoaded = true;
        }
        return client.options.getFov().getValue();
    }

    @Override
    protected void setValue(MinecraftClient client, int value) {
        int clampedValue = ModConfig.clampFOV(value);
        client.options.getFov().setValue(clampedValue);
        // Lưu vào config
        ModConfig.setCurrentFOV(clampedValue);

        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] FOV changed to: " + clampedValue);
        }
    }

    /**
     * Load FOV từ config và apply vào game
     */
    private void loadAndApplyFOV(MinecraftClient client) {
        int savedFOV = ModConfig.getCurrentFOV();
        client.options.getFov().setValue(savedFOV);

        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Loaded FOV from config: " + savedFOV);
        }
    }

    @Override
    protected String getFeatureNameKey() {
        return TranslationKeys.FOV_NAME;
    }

    @Override
    protected void showChangeMessage(MinecraftClient client, int newValue) {
        sendFeatureChangedMessage(client, TranslationKeys.FOV_CHANGED, newValue);
    }

    @Override
    protected void showMaxMessage(MinecraftClient client) {
        sendFeatureMessage(client, TranslationKeys.FOV_MAX, TranslationKeys.MESSAGE_MAX, maxValue);
    }

    @Override
    protected void showMinMessage(MinecraftClient client) {
        sendFeatureMessage(client, TranslationKeys.FOV_MIN, TranslationKeys.MESSAGE_MIN, minValue);
    }

    @Override
    public boolean isEnabled() {
        return ModConfig.ENABLE_FOV;
    }
}
