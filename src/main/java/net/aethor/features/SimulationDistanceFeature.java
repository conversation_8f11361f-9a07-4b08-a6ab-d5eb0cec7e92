package net.aethor.features;

import net.aethor.config.ModConfig;
import net.aethor.utils.TranslationKeys;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.util.InputUtil;

/**
 * Feature để điều khiển Simulation Distance bằng phím tắt
 * Kế thừa từ AbstractDistanceFeature với debouncing logic
 */
public class SimulationDistanceFeature extends AbstractDistanceFeature {

    private boolean simulationDistanceLoaded = false;

    public SimulationDistanceFeature() {
        super(
                TranslationKeys.SIMULATION_DISTANCE_FEATURE,
                TranslationKeys.INCREASE_SIMULATION_DISTANCE,
                TranslationKeys.DECREASE_SIMULATION_DISTANCE,
                TranslationKeys.QUICK_SETTING_CATEGORY,
                InputUtil.UNKNOWN_KEY.getCode(), // Không có phím mặc định
                InputUtil.UNKNOWN_KEY.getCode(), // <PERSON>hông có phím mặc định
                ModConfig.MIN_SIMULATION_DISTANCE,
                ModConfig.MAX_SIMULATION_DISTANCE,
                ModConfig.SIMULATION_STEP
        );
    }

    @Override
    protected int getCurrentValue(MinecraftClient client) {
        // Load từ config lần đầu khi player join
        if (!simulationDistanceLoaded && client.player != null) {
            loadAndApplySimulationDistance(client);
            simulationDistanceLoaded = true;
        }
        return client.options.getSimulationDistance().getValue();
    }

    @Override
    protected void setValue(MinecraftClient client, int value) {
        int clampedValue = ModConfig.clampSimulationDistance(value);
        client.options.getSimulationDistance().setValue(clampedValue);
        // Lưu vào config
        ModConfig.setCurrentSimulationDistance(clampedValue);

        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Simulation Distance changed to: " + clampedValue);
        }
    }

    /**
     * Load simulation distance từ config và apply vào game
     */
    private void loadAndApplySimulationDistance(MinecraftClient client) {
        int savedSimulationDistance = ModConfig.getCurrentSimulationDistance();
        client.options.getSimulationDistance().setValue(savedSimulationDistance);

        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Loaded simulation distance from config: " + savedSimulationDistance);
        }
    }

    @Override
    protected String getFeatureNameKey() {
        return TranslationKeys.SIMULATION_DISTANCE_NAME;
    }

    @Override
    protected void showChangeMessage(MinecraftClient client, int newValue) {
        sendFeatureChangedMessage(client, TranslationKeys.SIMULATION_DISTANCE_CHANGED, newValue);
    }

    @Override
    protected void showMaxMessage(MinecraftClient client) {
        sendFeatureMessage(client, TranslationKeys.SIMULATION_DISTANCE_MAX, TranslationKeys.MESSAGE_MAX, maxValue);
    }

    @Override
    protected void showMinMessage(MinecraftClient client) {
        sendFeatureMessage(client, TranslationKeys.SIMULATION_DISTANCE_MIN, TranslationKeys.MESSAGE_MIN, minValue);
    }

    @Override
    public boolean isEnabled() {
        return ModConfig.ENABLE_SIMULATION_DISTANCE;
    }
}