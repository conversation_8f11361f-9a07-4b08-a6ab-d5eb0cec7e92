package net.aethor.features;

import net.aethor.config.ModConfig;
import net.aethor.utils.TranslationKeys;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;

/**
 * Feature để zoom bằng phím tắt
 * Hold-to-zoom: Giữ phím thì zoom (FOV = 30°), thả phím thì trở về FOV gốc
 * Không hiển thị action messages
 */
public class ZoomFeature {

    private final KeyBinding zoomKey;
    private boolean isZoomed = false;
    private int originalFOV = 70; // Gi<PERSON> trị fallback, sẽ được cập nhật từ game

    public ZoomFeature() {
        // Đăng ký key binding cho hold-to-zoom
        this.zoomKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
                TranslationKeys.TOGGLE_ZOOM,
                InputUtil.Type.KEYSYM,
                InputUtil.UNKNOWN_KEY.getCode(), // Không có phím mặc định
                TranslationKeys.QUICK_SETTING_CATEGORY
        ));
    }

    /**
     * Xử lý input từ bàn phím cho zoom feature
     */
    public void handleInput(MinecraftClient client) {
        if (client.player == null) return;

        // Kiểm tra trạng thái phím zoom (hold-to-zoom)
        boolean shouldZoom = zoomKey.isPressed();

        if (shouldZoom && !isZoomed) {
            // Bắt đầu zoom
            startZoom(client);
        } else if (!shouldZoom && isZoomed) {
            // Kết thúc zoom
            stopZoom(client);
        }
    }

    /**
     * Bắt đầu zoom (khi nhấn phím)
     */
    private void startZoom(MinecraftClient client) {
        // Lưu FOV hiện tại và set zoom FOV
        originalFOV = client.options.getFov().getValue();
        client.options.getFov().setValue(ModConfig.ZOOM_FOV);
        isZoomed = true;

        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Zoom started, FOV set to: " + ModConfig.ZOOM_FOV);
        }
    }

    /**
     * Kết thúc zoom (khi thả phím)
     */
    private void stopZoom(MinecraftClient client) {
        // Trở về FOV gốc
        client.options.getFov().setValue(originalFOV);
        isZoomed = false;

        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Zoom stopped, FOV restored to: " + originalFOV);
        }
    }

    /**
     * Reset zoom state (dùng khi cần cleanup)
     */
    public void resetZoom(MinecraftClient client) {
        if (isZoomed && client != null) {
            stopZoom(client);
        }
    }

    // Getters

    public String getFeatureName() {
        return TranslationKeys.ZOOM_FEATURE;
    }

    public KeyBinding getToggleZoomKey() {
        return zoomKey;
    }

    public boolean isEnabled() {
        return ModConfig.ENABLE_ZOOM;
    }
}
