package net.aethor.features;

import net.aethor.config.ModConfig;
import net.aethor.utils.TranslationKeys;
import net.minecraft.client.MinecraftClient;
import net.minecraft.sound.SoundCategory;

/**
 * Feature để tắt bật âm thanh jukebox bằng phím tắt
 * <PERSON><PERSON><PERSON><PERSON> khiển SoundCategory.RECORD (jukebox, note blocks, etc.)
 * Có 2 trạng thái: ON/OFF
 */
public class JukeboxSoundFeature extends AbstractSoundFeature {

    public JukeboxSoundFeature() {
        super();
    }

    // Abstract method implementations

    @Override
    protected SoundCategory getSoundCategory() {
        return SoundCategory.RECORDS;
    }

    @Override
    protected String getToggleKeyTranslation() {
        return TranslationKeys.TOGGLE_JUKEBOX_SOUND;
    }

    @Override
    protected String getKeyCategoryTranslation() {
        return TranslationKeys.QUICK_SETTING_CATEGORY;
    }

    @Override
    protected String getFeatureNameTranslation() {
        return TranslationKeys.JUKEBOX_SOUND_NAME;
    }

    @Override
    protected String getOnMessageTranslation() {
        return TranslationKeys.MESSAGE_ON;
    }

    @Override
    protected String getOffMessageTranslation() {
        return TranslationKeys.MESSAGE_OFF;
    }

    @Override
    public String getFeatureName() {
        return TranslationKeys.JUKEBOX_SOUND_FEATURE;
    }

    @Override
    public boolean isEnabled() {
        return ModConfig.ENABLE_JUKEBOX_SOUND;
    }

    @Override
    protected double getDefaultRestoreVolume() {
        return ModConfig.JUKEBOX_VOLUME;
    }

    @Override
    protected double getSavedOriginalVolume() {
        return ModConfig.getJukeboxOriginalVolume();
    }

    @Override
    protected void setSavedOriginalVolume(double volume) {
        ModConfig.setJukeboxOriginalVolume(volume);
    }

    // Legacy methods for backward compatibility

    /**
     * @deprecated Use resetSound() instead
     */
    @Deprecated
    public void resetJukeboxSound(MinecraftClient client) {
        resetSound(client);
    }

    /**
     * @deprecated Use isSoundOn() instead
     */
    @Deprecated
    public boolean isJukeboxSoundOn() {
        return isSoundOn();
    }

    /**
     * @deprecated Use getMinecraftOriginalVolume() instead
     */
    @Deprecated
    public double getMinecraftOriginalJukeboxVolume() {
        return getMinecraftOriginalVolume();
    }
}
