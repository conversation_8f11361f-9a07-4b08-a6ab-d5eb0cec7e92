package net.aethor.features;

import net.aethor.config.ModConfig;
import net.aethor.utils.TranslationKeys;
import net.minecraft.client.MinecraftClient;
import net.minecraft.sound.SoundCategory;

/**
 * Feature để tắt bật âm thanh hostile mobs bằng phím tắt
 * <PERSON><PERSON><PERSON><PERSON> khiển SoundCategory.HOSTILE (zombie, skeleton, creeper, etc.)
 * Có 2 trạng thái: ON/OFF
 */
public class HostileMobSoundFeature extends AbstractSoundFeature {

    public HostileMobSoundFeature() {
        super();
    }

    // Abstract method implementations

    @Override
    protected SoundCategory getSoundCategory() {
        return SoundCategory.HOSTILE;
    }

    @Override
    protected String getToggleKeyTranslation() {
        return TranslationKeys.TOGGLE_HOSTILE_MOB_SOUND;
    }

    @Override
    protected String getKeyCategoryTranslation() {
        return TranslationKeys.QUICK_SETTING_CATEGORY;
    }

    @Override
    protected String getFeatureNameTranslation() {
        return TranslationKeys.HOSTILE_MOB_SOUND_NAME;
    }

    @Override
    protected String getOnMessageTranslation() {
        return TranslationKeys.MESSAGE_ON;
    }

    @Override
    protected String getOffMessageTranslation() {
        return TranslationKeys.MESSAGE_OFF;
    }

    @Override
    public String getFeatureName() {
        return TranslationKeys.HOSTILE_MOB_SOUND_FEATURE;
    }

    @Override
    public boolean isEnabled() {
        return ModConfig.ENABLE_HOSTILE_MOB_SOUND;
    }

    @Override
    protected double getDefaultRestoreVolume() {
        return ModConfig.HOSTILE_MOB_VOLUME;
    }

    @Override
    protected double getSavedOriginalVolume() {
        return ModConfig.getHostileMobOriginalVolume();
    }

    @Override
    protected void setSavedOriginalVolume(double volume) {
        ModConfig.setHostileMobOriginalVolume(volume);
    }

    // Legacy methods for backward compatibility

    /**
     * @deprecated Use resetSound() instead
     */
    @Deprecated
    public void resetHostileSound(MinecraftClient client) {
        resetSound(client);
    }

    /**
     * @deprecated Use isSoundOn() instead
     */
    @Deprecated
    public boolean isHostileSoundOn() {
        return isSoundOn();
    }

    /**
     * @deprecated Use getMinecraftOriginalVolume() instead
     */
    @Deprecated
    public double getMinecraftOriginalHostileVolume() {
        return getMinecraftOriginalVolume();
    }
}