package net.aethor;

import net.aethor.config.ModConfig;
import net.aethor.features.BrightnessFeature;
import net.aethor.render.BrightnessRenderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test cho brightness system mới
 * Đảm bảo rằng brightness hoạt động HOÀN TOÀN TÁCH BIỆT khỏi Minecraft options
 */
public class BrightnessTest {

    private BrightnessRenderer brightnessRenderer;
    private BrightnessFeature brightnessFeature;

    @BeforeEach
    void setUp() {
        // Clear tất cả system properties trước khi test
        System.clearProperty("quicksetting.brightness.value");
        System.clearProperty("quicksetting.brightness.display");
        System.clearProperty("opengl.brightness.multiplier");
        System.clearProperty("mod.brightness.override");
        System.clearProperty("minecraft.gamma");
        
        brightnessRenderer = BrightnessRenderer.getInstance();
        brightnessRenderer.initialize();
    }

    @AfterEach
    void tearDown() {
        if (brightnessRenderer != null) {
            brightnessRenderer.cleanup();
        }
        
        // Clear tất cả system properties sau test
        System.clearProperty("quicksetting.brightness.value");
        System.clearProperty("quicksetting.brightness.display");
        System.clearProperty("opengl.brightness.multiplier");
        System.clearProperty("mod.brightness.override");
        System.clearProperty("minecraft.gamma");
    }

    @Test
    void testBrightnessRendererInitialization() {
        assertNotNull(brightnessRenderer);
        assertEquals(1.0f, brightnessRenderer.getCurrentBrightnessMultiplier(), 0.01f);
        assertFalse(brightnessRenderer.isBrightnessEffectActive());
    }

    @Test
    void testSetBrightnessMultiplier() {
        // Test set brightness multiplier
        brightnessRenderer.setBrightnessMultiplier(2.0f);
        
        assertEquals(2.0f, brightnessRenderer.getCurrentBrightnessMultiplier(), 0.01f);
        assertTrue(brightnessRenderer.isBrightnessEffectActive());
        
        // Kiểm tra system property
        String multiplierStr = System.getProperty("opengl.brightness.multiplier");
        assertNotNull(multiplierStr);
        assertEquals(2.0f, Float.parseFloat(multiplierStr), 0.01f);
    }

    @Test
    void testBrightnessMultiplierClamping() {
        // Test giá trị quá thấp
        brightnessRenderer.setBrightnessMultiplier(-1.0f);
        assertEquals(0.1f, brightnessRenderer.getCurrentBrightnessMultiplier(), 0.01f);
        
        // Test giá trị quá cao
        brightnessRenderer.setBrightnessMultiplier(100.0f);
        assertEquals(16.0f, brightnessRenderer.getCurrentBrightnessMultiplier(), 0.01f);
        
        // Test giá trị bình thường
        brightnessRenderer.setBrightnessMultiplier(5.0f);
        assertEquals(5.0f, brightnessRenderer.getCurrentBrightnessMultiplier(), 0.01f);
    }

    @Test
    void testBrightnessReset() {
        // Set brightness khác 1.0
        brightnessRenderer.setBrightnessMultiplier(3.0f);
        assertTrue(brightnessRenderer.isBrightnessEffectActive());
        
        // Reset brightness
        brightnessRenderer.resetBrightnessEffect();
        assertEquals(1.0f, brightnessRenderer.getCurrentBrightnessMultiplier(), 0.01f);
        assertFalse(brightnessRenderer.isBrightnessEffectActive());
    }

    @Test
    void testSystemPropertyIntegration() {
        // Test set qua system property
        System.setProperty("opengl.brightness.multiplier", "4.0");
        
        // Apply brightness effect để đọc từ system property
        brightnessRenderer.applyBrightnessEffect();
        
        assertEquals(4.0f, brightnessRenderer.getCurrentBrightnessMultiplier(), 0.01f);
        assertTrue(brightnessRenderer.isBrightnessEffectActive());
    }

    @Test
    void testCleanup() {
        // Set brightness
        brightnessRenderer.setBrightnessMultiplier(2.5f);
        assertTrue(brightnessRenderer.isBrightnessEffectActive());
        
        // Cleanup
        brightnessRenderer.cleanup();
        assertEquals(1.0f, brightnessRenderer.getCurrentBrightnessMultiplier(), 0.01f);
        assertFalse(brightnessRenderer.isBrightnessEffectActive());
    }

    @Test
    void testNoMinecraftOptionsConflict() {
        // Test rằng brightness system không chạm vào bất kỳ Minecraft options nào
        
        // Set brightness cao
        brightnessRenderer.setBrightnessMultiplier(8.0f);
        
        // Kiểm tra rằng không có property nào liên quan đến Minecraft gamma
        assertNull(System.getProperty("minecraft.gamma"));
        
        // Kiểm tra rằng chỉ có properties của mod
        assertNotNull(System.getProperty("opengl.brightness.multiplier"));
        assertEquals("8.0", System.getProperty("opengl.brightness.multiplier"));
        
        System.out.println("✅ SUCCESS: Brightness system hoạt động HOÀN TOÀN TÁCH BIỆT khỏi Minecraft options!");
    }

    @Test
    void testMultipleBrightnessChanges() {
        // Test nhiều lần thay đổi brightness
        float[] testValues = {0.5f, 1.5f, 3.0f, 0.8f, 12.0f, 1.0f};
        
        for (float value : testValues) {
            brightnessRenderer.setBrightnessMultiplier(value);
            
            // Clamp expected value
            float expectedValue = Math.max(0.1f, Math.min(value, 16.0f));
            assertEquals(expectedValue, brightnessRenderer.getCurrentBrightnessMultiplier(), 0.01f);
            
            // Apply effect
            brightnessRenderer.applyBrightnessEffect();
            
            // Verify system property
            String multiplierStr = System.getProperty("opengl.brightness.multiplier");
            assertNotNull(multiplierStr);
            assertEquals(expectedValue, Float.parseFloat(multiplierStr), 0.01f);
        }
    }
}
