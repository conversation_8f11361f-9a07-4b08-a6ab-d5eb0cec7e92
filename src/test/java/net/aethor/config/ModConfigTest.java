package net.aethor.config;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class để kiểm tra ModConfig đọc từ JSON đúng cách
 */
public class ModConfigTest {

    @Test
    public void testConfigValuesLoadedFromJson() {
        // Test mod info
        assertEquals("quicksettingkeymod", ModConfig.MOD_ID);
        assertEquals("QuickSettingKeyMod", ModConfig.MOD_NAME);
        
        // Test render distance settings
        assertEquals(2, ModConfig.MIN_RENDER_DISTANCE);
        assertEquals(32, ModConfig.MAX_RENDER_DISTANCE);
        assertEquals(1, ModConfig.DEFAULT_RENDER_STEP);
        
        // Test simulation distance settings
        assertEquals(5, ModConfig.MIN_SIMULATION_DISTANCE);
        assertEquals(32, ModConfig.MAX_SIMULATION_DISTANCE);
        assertEquals(1, ModConfig.DEFAULT_SIMULATION_STEP);
        
        // Test FOV settings
        assertEquals(30, ModConfig.MIN_FOV);
        assertEquals(110, ModConfig.MAX_FOV);
        assertEquals(5, ModConfig.DEFAULT_FOV_STEP);
        assertEquals(30, ModConfig.ZOOM_FOV);
        assertEquals(70, ModConfig.DEFAULT_FOV);
        
        // Test brightness settings
        assertEquals(0.0, ModConfig.BRIGHTNESS_MIN, 0.001);
        assertEquals(12.0, ModConfig.BRIGHTNESS_MAX, 0.001);
        assertEquals(0.5, ModConfig.BRIGHTNESS_STEP, 0.001);
        
        // Test feature flags
        assertTrue(ModConfig.ENABLE_RENDER_DISTANCE);
        assertTrue(ModConfig.ENABLE_SIMULATION_DISTANCE);
        assertTrue(ModConfig.ENABLE_FOV);
        assertTrue(ModConfig.ENABLE_BRIGHTNESS);
        assertTrue(ModConfig.ENABLE_ZOOM);
        
        // Test sound settings
        assertEquals(0.5, ModConfig.MOB_SOUND_DEFAULT_RESTORE_VOLUME, 0.001);
        assertEquals(0.5, ModConfig.MUSIC_SOUND_DEFAULT_RESTORE_VOLUME, 0.001);
        assertEquals(0.5, ModConfig.JUKEBOX_SOUND_DEFAULT_RESTORE_VOLUME, 0.001);
        
        // Test debounce settings
        assertEquals(2000, ModConfig.DEBOUNCE_DELAY_MS);
        
        // Test debug settings
        assertFalse(ModConfig.DEBUG_MODE);
    }

    @Test
    public void testClampMethods() {
        // Test render distance clamping
        assertEquals(2, ModConfig.clampRenderDistance(1)); // Below min
        assertEquals(15, ModConfig.clampRenderDistance(15)); // Within range
        assertEquals(32, ModConfig.clampRenderDistance(50)); // Above max
        
        // Test simulation distance clamping
        assertEquals(5, ModConfig.clampSimulationDistance(3)); // Below min
        assertEquals(20, ModConfig.clampSimulationDistance(20)); // Within range
        assertEquals(32, ModConfig.clampSimulationDistance(40)); // Above max
        
        // Test FOV clamping
        assertEquals(30, ModConfig.clampFOV(20)); // Below min
        assertEquals(90, ModConfig.clampFOV(90)); // Within range
        assertEquals(110, ModConfig.clampFOV(150)); // Above max
    }

    @Test
    public void testDefaultValueMethods() {
        // Test default values từ config
        assertEquals(12, ModConfig.getDefaultRenderDistance());
        assertEquals(12, ModConfig.getDefaultSimulationDistance());
        assertEquals(1.0, ModConfig.getDefaultBrightness(), 0.001);
    }
}
