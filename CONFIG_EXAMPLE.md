# C<PERSON>u hình QuickSettingKeyMod

Mod này sử dụng file `quicksettingkey.json` duy nhất để lưu trữ cả cấu hình mặc định và trạng thái hiện tại.

## Vị trí file config

- **Template**: `src/main/resources/quicksettingkey.json` (trong mod)
- **Active config**: `<minecraft_folder>/config/quicksettingkey.json` (được copy từ template lần đầu)

## Cấu trúc file config

File `quicksettingkey.json` chứa cả default values và current values:

```json
{
  "mod_info": {
    "mod_id": "quicksettingkeymod",
    "mod_name": "QuickSettingKeyMod",
    "config_version": "1.0"
  },
  "render_distance": {
    "min": 2,
    "max": 32,
    "default_step": 1,
    "default_value": 12,
    "current_value": 16
  },
  "simulation_distance": {
    "min": 5,
    "max": 32,
    "default_step": 1,
    "default_value": 12,
    "current_value": 12
  },
  "fov": {
    "min": 30,
    "max": 110,
    "default_step": 5,
    "default_value": 70,
    "zoom_fov": 30,
    "current_value": 90
  },
  "brightness": {
    "min": 0.0,
    "max": 12.0,
    "step": 0.5,
    "default_value": 1.0,
    "current_value": 12.0
  },
  "features": {
    "render_distance": true,
    "simulation_distance": true,
    "fov": true,
    "brightness": true,
    "zoom": true,
    "hostile_mob_sound": true,
    "friendly_mob_sound": true,
    "music_sound": true,
    "jukebox_sound": true
  },
  "sound_settings": {
    "mob_sound_default_restore_volume": 0.5,
    "music_sound_default_restore_volume": 0.5,
    "jukebox_sound_default_restore_volume": 0.5
  },
  "debounce": {
    "delay_ms": 2000
  },
  "debug": {
    "enabled": false
  }
}
```

## Cách tùy chỉnh

### Thay đổi giới hạn render distance
```json
"render_distance": {
  "min": 4,        // Tăng min từ 2 lên 4
  "max": 48,       // Tăng max từ 32 lên 48
  "default_step": 2,  // Tăng step từ 1 lên 2
  "default_value": 16 // Thay đổi giá trị mặc định
}
```

### Tắt một số features
```json
"features": {
  "render_distance": true,
  "simulation_distance": false,  // Tắt simulation distance
  "fov": true,
  "brightness": false,           // Tắt brightness
  "zoom": true,
  "hostile_mob_sound": true,
  "friendly_mob_sound": true,
  "music_sound": true,
  "jukebox_sound": true
}
```

### Thay đổi debounce delay
```json
"debounce": {
  "delay_ms": 1000  // Giảm delay từ 2000ms xuống 1000ms
}
```

### Bật debug mode
```json
"debug": {
  "enabled": true   // Bật debug để xem log chi tiết
}
```

## Cách hoạt động

1. **Lần đầu chạy**: Mod copy file template từ resources sang config folder
2. **Khi thay đổi settings**: `current_value` được cập nhật trong file config
3. **Khi thoát game**: File config được save để giữ lại trạng thái
4. **Lần khởi động tiếp theo**: Mod đọc `current_value` để restore settings

## Ý nghĩa các trường

- **`default_value`**: Giá trị mặc định ban đầu
- **`current_value`**: Giá trị hiện tại mà người dùng đã chỉnh
- **`min`, `max`**: Giới hạn cho phép
- **`step`**: Bước nhảy khi tăng/giảm
- **`enabled`**: Bật/tắt feature

## Lưu ý

- File config được tự động tạo từ template lần đầu
- `current_value` được cập nhật tự động khi thay đổi settings
- Nếu file config bị lỗi, mod sẽ tạo lại từ template
- Có thể edit file config để thay đổi giới hạn và current values
- Sau khi edit file config, cần restart Minecraft để áp dụng

## Ưu điểm của hệ thống config mới

1. **Đơn giản**: Chỉ 1 file config duy nhất
2. **Đầy đủ**: Chứa cả default và current values
3. **Persistent**: Settings được lưu trữ giữa các lần chơi
4. **Dễ tùy chỉnh**: Có thể edit trực tiếp file JSON
5. **An toàn**: Có fallback nếu file bị lỗi
6. **Tự động**: Current values được cập nhật tự động
